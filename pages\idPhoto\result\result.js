// pages/idPhoto/result/result.js
const idPhotoAPI = require('../../../utils/api/idPhotoAPI');

Page({
  data: {
    loading: true,
    loadingText: '正在处理照片...',
    error: false,
    errorMessage: '',
    saving: false,

    // 原始参数
    originalImage: '',
    sizeKey: '',
    sizeName: '',

    // 处理结果
    currentImage: '', // 当前显示的图片
    hdImage: '', // 高清版本
    transparentImage: '', // 透明底图

    // 尺寸信息
    sizeInfo: {},

    // 背景色选择
    selectedBackground: 'white',
    backgroundOptions: [], // 从服务器获取的颜色选项

    // 生成结果信息
    resultInfo: {}
  },

  onLoad(options) {
    console.log('证件照结果页面加载，参数:', options);

    // 解析参数
    const { image, size, sizeName } = options;
    this.setData({
      originalImage: decodeURIComponent(image),
      sizeKey: size,
      sizeName: decodeURIComponent(sizeName || ''),
      sizeInfo: {
        name: decodeURIComponent(sizeName || ''),
        size: size
      }
    });

    // 加载颜色选项并开始处理照片
    this.loadColorsAndProcess();
  },

  /**
   * 加载颜色选项并开始处理照片
   */
  async loadColorsAndProcess() {
    try {
      this.setData({
        loading: true,
        loadingText: '正在加载配置...'
      });

      // 获取颜色列表
      const colorsResult = await idPhotoAPI.getColors();
      console.log('颜色列表:', colorsResult);

      if (colorsResult.success) {
        this.setData({
          backgroundOptions: colorsResult.data.colors || []
        });

        // 开始处理照片
        this.processPhoto();
      } else {
        throw new Error('获取颜色配置失败');
      }

    } catch (error) {
      console.error('加载配置失败:', error);
      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '加载配置失败，请重试'
      });
    }
  },

  /**
   * 处理照片
   */
  async processPhoto() {
    try {
      this.setData({
        loading: true,
        loadingText: '正在生成证件照...',
        error: false
      });

      // 调用新的证件照生成API
      const result = await idPhotoAPI.generateIDPhoto({
        imagePath: this.data.originalImage,
        size: this.data.sizeKey,
        color: 'white' // 默认白色背景
      });

      console.log('证件照生成结果:', result);
      console.log('result.data:', result.data);
      console.log('result.data.imageBase64 length:', result.data?.imageBase64?.length);
      console.log('result.data.imageBase64 preview:', result.data?.imageBase64?.substring(0, 100));

      if (result.success) {
        // 确保base64数据有正确的前缀
        const formatBase64 = (base64Data) => {
          console.log('formatBase64 input:', base64Data ? `${base64Data.substring(0, 50)}...` : 'null');
          if (!base64Data) return '';
          if (base64Data.startsWith('data:')) return base64Data;
          const formatted = `data:image/jpeg;base64,${base64Data}`;
          console.log('formatBase64 output preview:', formatted.substring(0, 100));
          return formatted;
        };

        const currentImage = formatBase64(result.data.imageBase64);
        const hdImage = formatBase64(result.data.hdImageBase64);
        const transparentImage = formatBase64(result.data.transparentBase64);

        console.log('设置图片数据:');
        console.log('currentImage length:', currentImage.length);
        console.log('hdImage length:', hdImage.length);
        console.log('transparentImage length:', transparentImage.length);

        this.setData({
          currentImage: currentImage,
          hdImage: hdImage,
          transparentImage: transparentImage,
          resultInfo: {
            size: result.data.size,
            sizeName: result.data.sizeName,
            color: result.data.color,
            colorName: result.data.colorName,
            dimensions: result.data.dimensions
          },
          sizeInfo: {
            name: result.data.sizeName,
            size: result.data.size,
            dimensions: result.data.dimensions
          },
          selectedBackground: 'white',
          loading: false
        });
      } else {
        throw new Error(result.message || '生成证件照失败');
      }

    } catch (error) {
      console.error('处理照片失败:', error);
      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '处理失败，请重试'
      });
    }
  },

  /**
   * 选择背景色
   */
  selectBackground(e) {
    const color = e.currentTarget.dataset.color;
    console.log('选择背景色:', color);

    this.setData({
      selectedBackground: color
    });

    this.generateBackground(color);
  },

  /**
   * 生成背景色
   */
  async generateBackground(colorKey) {
    try {
      if (colorKey === 'transparent') {
        // 透明背景直接使用透明图
        this.setData({
          currentImage: this.data.transparentImage,
          loading: false
        });
        return;
      }

      this.setData({
        loading: true,
        loadingText: '正在更换背景色...'
      });

      // 重新调用生成API，使用新的背景色
      const result = await idPhotoAPI.generateIDPhoto({
        imagePath: this.data.originalImage,
        size: this.data.sizeKey,
        color: colorKey
      });

      console.log('背景色生成结果:', result);
      console.log('背景色 result.data:', result.data);
      console.log('背景色 result.data.imageBase64 length:', result.data?.imageBase64?.length);

      if (result.success) {
        // 确保base64数据有正确的前缀
        const formatBase64 = (base64Data) => {
          console.log('背景色 formatBase64 input:', base64Data ? `${base64Data.substring(0, 50)}...` : 'null');
          if (!base64Data) return '';
          if (base64Data.startsWith('data:')) return base64Data;
          const formatted = `data:image/jpeg;base64,${base64Data}`;
          console.log('背景色 formatBase64 output preview:', formatted.substring(0, 100));
          return formatted;
        };

        this.setData({
          currentImage: formatBase64(result.data.imageBase64),
          hdImage: formatBase64(result.data.hdImageBase64),
          resultInfo: {
            ...this.data.resultInfo,
            color: result.data.color,
            colorName: result.data.colorName
          },
          loading: false
        });
      } else {
        throw new Error(result.message || '更换背景色失败');
      }

    } catch (error) {
      console.error('生成背景色失败:', error);
      this.setData({
        loading: false
      });
      // 移除错误提示，避免打扰用户
    }
  },

  /**
   * 保存到相册
   */
  async saveToAlbum() {
    if (!this.data.currentImage) {
      wx.showToast({
        title: '没有可保存的图片',
        icon: 'error'
      });
      return;
    }

    try {
      this.setData({ saving: true });

      // 保存标准版本
      await this.saveImageToAlbum(this.data.currentImage, '标准版');

      // 如果有高清版本，也保存高清版本
      if (this.data.hdImage) {
        await this.saveImageToAlbum(this.data.hdImage, '高清版');
      }

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('保存失败:', error);
      // 简化错误提示
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 保存单张图片到相册
   */
  saveImageToAlbum(imageBase64, version) {
    return new Promise((resolve, reject) => {
      // 将base64转换为临时文件
      const base64Data = imageBase64.replace(/^data:image\/\w+;base64,/, '');
      const buffer = wx.base64ToArrayBuffer(base64Data);

      const fs = wx.getFileSystemManager();
      const fileName = `idphoto_${version}_${Date.now()}.jpg`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      fs.writeFile({
        filePath: filePath,
        data: buffer,
        encoding: 'binary',
        success: () => {
          // 请求保存到相册权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              wx.saveImageToPhotosAlbum({
                filePath: filePath,
                success: resolve,
                fail: reject
              });
            },
            fail: () => {
              // 权限被拒绝，引导用户手动开启
              wx.showModal({
                title: '需要相册权限',
                content: '需要获取您的相册权限，请在设置中开启',
                showCancel: false,
                confirmText: '去设置',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          wx.saveImageToPhotosAlbum({
                            filePath: filePath,
                            success: resolve,
                            fail: reject
                          });
                        } else {
                          reject(new Error('用户拒绝授权'));
                        }
                      }
                    });
                  } else {
                    reject(new Error('用户拒绝授权'));
                  }
                }
              });
            }
          });
        },
        fail: reject
      });
    });
  },

  /**
   * 重新制作
   */
  remakePhoto() {
    wx.navigateBack();
  },

  /**
   * 重试处理
   */
  retryProcess() {
    this.processPhoto();
  },

  /**
   * 返回
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 图片加载成功
   */
  onImageLoad(e) {
    console.log('图片加载成功:', e.detail);
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
    console.error('图片加载失败:', e.detail);
    console.error('当前图片src:', this.data.currentImage);
    wx.showToast({
      title: '图片加载失败',
      icon: 'none'
    });
  }
});
