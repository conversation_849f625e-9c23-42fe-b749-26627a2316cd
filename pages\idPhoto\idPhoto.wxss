.container {
  padding: 40rpx 20rpx 20rpx 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}



/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #4B8BF5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 尺寸选择区域 */
.size-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.size-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.size-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  transition: border-color 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
}

.size-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: -1;
}

.size-item.selected {
  border-color: #4B8BF5;
  color: white;
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(75, 139, 245, 0.25);
}

.size-item.selected::before {
  opacity: 1;
}

.size-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
}

.size-desc {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
  position: relative;
  z-index: 1;
}

.size-mm {
  font-size: 22rpx;
  opacity: 0.7;
  margin-bottom: 4rpx;
  position: relative;
  z-index: 1;
}



/* 照片上传区域 */
.upload-section {
  margin-bottom: 40rpx;
}

.upload-area {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  position: relative;
}

.upload-placeholder {
  padding: 80rpx 40rpx;
  text-align: center;
  border: 2rpx dashed #ddd;
  margin: 20rpx;
  border-radius: 16rpx;
  background: #fafafa;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.upload-text {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.upload-preview {
  position: relative;
  height: 400rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-preview:active .upload-mask {
  opacity: 1;
}

.change-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 开始制作按钮 */
.action-section {
  margin-bottom: 40rpx;
}

.start-btn {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.start-btn.active {
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(75, 139, 245, 0.4);
}

.start-btn.disabled {
  background: #e0e0e0;
  color: #999;
}

.start-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(75, 139, 245, 0.3);
}

/* 使用说明 */
.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
