<view class="container">


  <!-- 结果展示区域 -->
  <view class="result-section">
    <!-- 证件照预览 -->
    <view class="photo-preview">
      <view class="preview-container">
        <!-- 内联加载动画 -->
        <view wx:if="{{loading}}" class="inline-loading">
          <view class="loading-spinner"></view>
          <text class="loading-text">{{loadingText}}</text>
        </view>
        <!-- 图片显示 -->
        <image
          wx:else
          class="result-image"
          src="{{currentImage}}"
          mode="aspectFit"
          show-menu-by-longpress="{{true}}"
          binderror="onImageError"
          bindload="onImageLoad"
        ></image>
      </view>
    </view>

    <!-- 底色选择 -->
    <view class="background-section">
      <text class="section-title">选择背景色</text>
      <view class="color-options">
        <view
          class="color-item {{selectedBackground === item.value ? 'selected' : ''}}"
          wx:for="{{backgroundOptions}}"
          wx:key="value"
          bindtap="selectBackground"
          data-color="{{item.value}}"
        >
          <view class="color-preview" style="background-color: #{{item.hex}};"></view>
          <text class="color-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="save-btn" bindtap="saveToAlbum" disabled="{{saving}}">
        <text wx:if="{{!saving}}">保存到相册</text>
        <text wx:else>保存中...</text>
      </button>
      <button class="remake-btn" bindtap="remakePhoto">重新制作</button>
    </view>

    <!-- 移除高清版本提示，简化界面 -->
  </view>


</view>
