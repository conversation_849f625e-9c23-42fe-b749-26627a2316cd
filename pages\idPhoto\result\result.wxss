.container {
  padding: 40rpx 20rpx 20rpx 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}



/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #4B8BF5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

/* 结果展示区域 */
.result-section {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 证件照预览 */
.photo-preview {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-image {
  width: auto;
  height: auto;
  max-width: 400rpx;
  max-height: 500rpx;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 30rpx rgba(0,0,0,0.15);
  margin-bottom: 20rpx;
  display: block;
}



/* 底色选择 */
.background-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.color-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 16rpx;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.color-item.selected {
  border-color: #4B8BF5;
  background: #f0f7ff;
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(75, 139, 245, 0.2);
}

.color-preview {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
  border: 2rpx solid #ddd;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.color-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 操作按钮 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.save-btn, .remake-btn {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.save-btn {
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(75, 139, 245, 0.4);
}

.save-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(75, 139, 245, 0.3);
}

.save-btn:disabled {
  background: #e0e0e0;
  color: #999;
  box-shadow: none;
}

.remake-btn {
  background: white;
  color: #4B8BF5;
  border: 2rpx solid #4B8BF5;
}

.remake-btn:active {
  background: #f0f7ff;
}

/* 高清版本提示 */
.hd-section {
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1rpx solid #b3d9ff;
}

.hd-tip {
  text-align: center;
}

.hd-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #1976d2;
  display: block;
  margin-bottom: 8rpx;
}

.hd-desc {
  font-size: 24rpx;
  color: #1976d2;
  opacity: 0.8;
  display: block;
}


