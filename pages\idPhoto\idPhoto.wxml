<view class="container">


  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-section">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载配置信息...</text>
  </view>

  <!-- 尺寸选择区域 -->
  <view wx:else class="size-section">
    <text class="section-title">选择证件照尺寸</text>
    <view class="size-grid">
      <view
        class="size-item {{selectedSize === item.value ? 'selected' : ''}}"
        wx:for="{{sizeOptions}}"
        wx:key="value"
        bindtap="selectSize"
        data-size="{{item.value}}"
      >
        <view class="size-name">{{item.name}}</view>
        <view class="size-desc">{{item.width}}×{{item.height}}像素</view>
        <view class="size-mm">{{item.print_size}}</view>
        <!--  <view class="size-description">{{item.description}}</view> -->
      </view>
    </view>
  </view>

  <!-- 照片上传区域 -->
  <view class="upload-section">
    <text class="section-title">上传照片</text>
    <view class="upload-area" bindtap="chooseImage">
      <view wx:if="{{!selectedImage}}" class="upload-placeholder">
        <view class="upload-icon">📷</view>
        <text class="upload-text">点击选择照片</text>
        <text class="upload-hint">支持JPG、PNG格式，建议像素不低于300×400</text>
      </view>
      <view wx:else class="upload-preview">
        <image class="preview-image" src="{{selectedImage}}" mode="aspectFit"></image>
        <view class="upload-mask" bindtap="chooseImage">
          <text class="change-text">重新选择</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 开始制作按钮 -->
  <view class="action-section">
    <button 
      class="start-btn {{selectedSize && selectedImage ? 'active' : 'disabled'}}"
      bindtap="startProcess"
      disabled="{{!selectedSize || !selectedImage || processing}}"
    >
      <text wx:if="{{!processing}}">开始制作证件照</text>
      <text wx:else>处理中...</text>
    </button>
  </view>

  <!-- 使用说明 -->
  <view class="tips-section">
    <text class="tips-title">使用说明</text>
    <view class="tips-list">
      <text class="tip-item">• 请选择清晰的正面照片</text>
      <text class="tip-item">• 照片中人物应居中，面部清晰可见</text>
      <text class="tip-item">• 建议使用光线充足的照片</text>
      <text class="tip-item">• 系统将自动进行人脸识别和抠图处理</text>
    </view>
  </view>
</view>
